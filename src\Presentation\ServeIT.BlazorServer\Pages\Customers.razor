@page "/customers"
@using ServeIT.BlazorServer.Services
@using ServeIT.Models.DTOs
@using ServeIT.Models.Common
@using Blazored.Toast.Services
@inject ICustomerApiService CustomerService
@inject IToastService ToastService

<PageTitle>Customers - ServeIT Modern</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-5">
                        <i class="bi bi-people-fill text-primary"></i> Customers
                    </h1>
                    <p class="text-muted">Manage your customer database</p>
                </div>
                <button class="btn btn-primary" @onclick="ShowCreateModal">
                    <i class="bi bi-plus-circle"></i> Add Customer
                </button>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="bi bi-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search customers..." 
                       @bind="searchTerm" @onkeypress="OnSearchKeyPress" />
                <button class="btn btn-outline-secondary" @onclick="SearchCustomers">
                    Search
                </button>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <button class="btn btn-outline-secondary" @onclick="RefreshData">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
                <button class="btn btn-outline-secondary">
                    <i class="bi bi-download"></i> Export
                </button>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading customers...</p>
        </div>
    }
    else if (customers?.Any() == true)
    {
        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Name</th>
                                <th>Mobile</th>
                                <th>Phone</th>
                                <th>City</th>
                                <th>Balance</th>
                                <th>Status</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var customer in customers)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary bg-gradient rounded-circle p-2 me-2">
                                                <i class="bi bi-person text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">@customer.Name</h6>
                                                @if (!string.IsNullOrEmpty(customer.SalesRepresentativeName))
                                                {
                                                    <small class="text-muted">Rep: @customer.SalesRepresentativeName</small>
                                                }
                                            </div>
                                        </div>
                                    </td>
                                    <td>@customer.Mobile</td>
                                    <td>@customer.Phone</td>
                                    <td>@customer.City</td>
                                    <td>
                                        <span class="@(customer.Balance >= 0 ? "text-success" : "text-danger")">
                                            @customer.Balance.ToString("C")
                                        </span>
                                    </td>
                                    <td>
                                        @if (customer.IsActive)
                                        {
                                            <span class="badge bg-success">Active</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">Inactive</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" @onclick="() => EditCustomer(customer)">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" @onclick="() => DeleteCustomer(customer)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

@code {
    private List<CustomerDto>? customers;
    private PagedResult<CustomerDto>? pagedResult;
    private string searchTerm = string.Empty;
    private bool isLoading = true;
    private PagingParameters pagingParameters = new() { PageSize = 10 };

    protected override async Task OnInitializedAsync()
    {
        await LoadCustomers();
    }

    private async Task LoadCustomers()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            ServiceResult<PagedResult<CustomerDto>> result;

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                result = await CustomerService.SearchCustomersAsync(searchTerm, pagingParameters);
            }
            else
            {
                result = await CustomerService.GetCustomersPagedAsync(pagingParameters);
            }

            if (result.IsSuccess && result.Data != null)
            {
                pagedResult = result.Data;
                customers = result.Data.Items.ToList();
            }
            else
            {
                ToastService.ShowError(result.Message ?? "Failed to load customers");
                customers = new List<CustomerDto>();
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"Error loading customers: {ex.Message}");
            customers = new List<CustomerDto>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadPage(int pageNumber)
    {
        pagingParameters.PageNumber = pageNumber;
        await LoadCustomers();
    }

    private async Task SearchCustomers()
    {
        pagingParameters.PageNumber = 1; // Reset to first page
        await LoadCustomers();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchCustomers();
        }
    }

    private async Task RefreshData()
    {
        searchTerm = string.Empty;
        pagingParameters.PageNumber = 1;
        await LoadCustomers();
        ToastService.ShowSuccess("Data refreshed successfully");
    }

    private void ShowCreateModal()
    {
        // TODO: Implement create customer modal
        ToastService.ShowInfo("Create customer functionality will be implemented");
    }

    private void EditCustomer(CustomerDto customer)
    {
        // TODO: Implement edit customer modal
        ToastService.ShowInfo($"Edit customer '{customer.Name}' functionality will be implemented");
    }

    private async Task DeleteCustomer(CustomerDto customer)
    {
        // TODO: Implement confirmation dialog
        if (await ConfirmDelete(customer.Name))
        {
            var result = await CustomerService.DeleteCustomerAsync(customer.Id);
            if (result.IsSuccess)
            {
                ToastService.ShowSuccess($"Customer '{customer.Name}' deleted successfully");
                await LoadCustomers();
            }
            else
            {
                ToastService.ShowError(result.Message ?? "Failed to delete customer");
            }
        }
    }

    private async Task<bool> ConfirmDelete(string customerName)
    {
        // Simple confirmation - in real app, use a proper modal
        return await Task.FromResult(true); // For now, always confirm
    }
}
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h3 class="mt-3">No customers found</h3>
            <p class="text-muted">Start by adding your first customer</p>
            <button class="btn btn-primary" @onclick="ShowCreateModal">
                <i class="bi bi-plus-circle"></i> Add Customer
            </button>
        </div>
    }
</div>
