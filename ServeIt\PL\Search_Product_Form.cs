﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ServeIt.PL
{
    public partial class Search_Product_Form : Form
    {

        public static Search_Product_Form Ser_Pro_Frm;

        public string Pro_Kind = string.Empty;
        public decimal Quantity;
        public int UnitID;
        public static Search_Product_Form CheckIns
        {
            get
            {
                return Ser_Pro_Frm;
            }
        }

        public static Search_Product_Form CreateIns
        {
            get
            {
                if (Ser_Pro_Frm == null)
                    Ser_Pro_Frm = new Search_Product_Form();
                return Ser_Pro_Frm;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            Ser_Pro_Frm = null;
            base.OnClosed(e);
        }

        private Search_Product_Form()
        {
            InitializeComponent();
            FormLoading = true;
            BL.StockBL stockbl = new BL.StockBL();
            CbStock.DataSource = stockbl.GetAllStock();
            CbStock.DisplayMember = "stock_name";
            CbStock.ValueMember = "stock_id";
            FormLoading = false;
        }

        bool FormLoading = false;
        private void Search_Product_Form_Load(object sender, EventArgs e)
        {
            FormLoading = true;
            BL.Category catbl = new BL.Category();
            CMBCat.DataSource = catbl.GetAllCat();
            CMBCat.DisplayMember = "cat_name";
            CMBCat.ValueMember = "cat_id";
            CMBCat.SelectedValue = -1;
            this.ActiveControl = TxtProName;
            FormLoading = false;
        }

        public DataTable ProductListDT = new DataTable();
        public void SearchForProduct()
        {
            if (!FormLoading)
            {
                BL.InvoiceBL invbl = new BL.InvoiceBL();
                BE.StockBE stockbe = new BE.StockBE();

                stockbe.Cat_Id = CMBCat.SelectedValue == null ? null : CMBCat.SelectedValue.ToString();
                stockbe.ProID = string.IsNullOrEmpty(TxtProId.Text) ? null : TxtProId.Text;
                stockbe.ProID2 = string.IsNullOrEmpty(TxtProId2.Text) ? null : TxtProId2.Text;
                stockbe.ProName = null;
                stockbe.StockId = CbStock.SelectedValue == null ? null : CbStock.SelectedValue.ToString();
                ProductListDT = invbl.SearchProductInInvocie(stockbe);
                PopulateDataGridView(dataGridView1,ProductListDT );
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            
                string rowFilter = string.Empty;
                if (!string.IsNullOrEmpty(TxtProName.Text))
                {
                    rowFilter = "pro_name like '%" + TxtProName.Text + "%'";

                    (dataGridView1.DataSource as DataTable).DefaultView.RowFilter = rowFilter;
                }
                else
                {
                    (dataGridView1.DataSource as DataTable).DefaultView.RowFilter = null;
                }
           
            //BindingSource bs = new BindingSource();
            //bs.DataSource = dataGridView1.DataSource;
            //if (string.IsNullOrEmpty(TxtProName.Text))
            //{
            //    bs.RemoveFilter();
            //}
            //else
            //{
            //    bs.Filter = "pro_name like '%" + TxtProName.Text + "%'";
            //}
           
        }

        private void PopulateDataGridView(DataGridView DGV, DataTable dt)
        {
            if (dt.Rows.Count != 0)
            {
                DGV.DataSource = dt;
                DGV.Columns["pro_id"].HeaderText = "كود الصنف";
                DGV.Columns["pro_id2"].HeaderText = "كود الصنف 2";
                DGV.Columns["pro_name"].HeaderText = "اسم الصنف";
                DGV.Columns["cat_name"].HeaderText = "الفئة";
                DGV.Columns["stock_name"].HeaderText = "المخزن";
                DGV.Columns["pro_kind"].HeaderText = "طبيعة الصنف";
                DGV.Columns["unit_name"].HeaderText = "الوحدة";
                //DGV.Columns["retail_price"].HeaderText = "سعر المستهلك في تعريف الصنف";
                //DGV.Columns["last_sale_Price"].HeaderText = "اخر سعر بيع";
                //DGV.Columns["last_customer_sale_price"].HeaderText = "اخر سعر بيع للعميل";
                DGV.Columns["pro_id2"].Visible = false;
                DGV.Columns["stock_name"].Visible = false;
            }
            else
            {
                DGV.DataSource = null;
                for (int i = 0; i < DGV.Rows.Count; i++)
                {
                    DGV.Rows[i].Visible = false;
                }
            }
        }


        public int? InvCount;
        public void AddProductToInvoice()
        {

            // prevent adding items if permission kind of type salse invoice or pur return or export permission or relocate or damage and Future Balance will be zero or less than

            string OwnerName = this.Owner.Name;

            if (OwnerName == "invoice_new")
            {
                foreach (Form frm in Application.OpenForms)
                {
                    if (frm.Name == "invoice_new")
                    {
                        invoice_new inv_old = (invoice_new)frm;
                        if (int.Parse(inv_old.LblInvCount.Text) == InvCount)
                        {
                            inv_old.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                            inv_old.dataGridView1.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                            inv_old.dataGridView1.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                            inv_old.dataGridView1.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                            inv_old.dataGridView1.CommitEdit(DataGridViewDataErrorContexts.Commit);
                            inv_old.dataGridView1.EndEdit(DataGridViewDataErrorContexts.Commit);
                        }
                    }
                }

                //SendKeys.Send("{left}");
            }
            else if (OwnerName == "Invoice_Cashier_Form")
            {
                foreach (Form frm in Application.OpenForms)
                {
                    if (frm.Name == "Invoice_Cashier_Form")
                    {
                        PL.invoices.Invoice_Cashier_Form inv_old = (PL.invoices.Invoice_Cashier_Form)frm;
                        if (int.Parse(inv_old.LblInvCount.Text) == InvCount)
                        {
                            inv_old.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                            inv_old.dataGridView1.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                            inv_old.dataGridView1.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                            inv_old.dataGridView1.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                            inv_old.dataGridView1.CommitEdit(DataGridViewDataErrorContexts.Commit);
                            inv_old.dataGridView1.EndEdit(DataGridViewDataErrorContexts.Commit);
                        }
                    }
                }

                //SendKeys.Send("{left}");
            }
            else if (OwnerName == "Product_First_Period_Form")
            {
                foreach (Form frm in Application.OpenForms)
                {
                    if (frm.Name == "Product_First_Period_Form")
                    {
                        PL.inventory.Product_First_Period_Form per_old = (PL.inventory.Product_First_Period_Form)frm;
                        
                            per_old.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                            per_old.dataGridView1.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                            per_old.dataGridView1.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                            per_old.dataGridView1.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                            per_old.dataGridView1.CommitEdit(DataGridViewDataErrorContexts.Commit);
                            per_old.dataGridView1.EndEdit(DataGridViewDataErrorContexts.Commit);
                        
                    }
                }

                //SendKeys.Send("{left}");
            }
            else if (OwnerName == "Collected_Item_Form")
            {
                PL.difinition.Collected_Item_Form.CheckIns.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                PL.difinition.Collected_Item_Form.CheckIns.dataGridView1.CurrentRow.Cells["pro_raw_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();

                PL.difinition.Collected_Item_Form.CheckIns.dataGridView1.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                PL.difinition.Collected_Item_Form.CheckIns.dataGridView1.CommitEdit(DataGridViewDataErrorContexts.Commit);
                PL.difinition.Collected_Item_Form.CheckIns.dataGridView1.EndEdit(DataGridViewDataErrorContexts.Commit);

            }
            else if (OwnerName == "inventory_damaged_permission_new")
            {
                PL.Manfacturing.inventory_damaged_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                PL.Manfacturing.inventory_damaged_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                PL.Manfacturing.inventory_damaged_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                PL.Manfacturing.inventory_damaged_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                PL.Manfacturing.inventory_damaged_permission_new.CheckIns.dataGridView1.CommitEdit(DataGridViewDataErrorContexts.Commit);
                PL.Manfacturing.inventory_damaged_permission_new.CheckIns.dataGridView1.EndEdit(DataGridViewDataErrorContexts.Commit);

                PL.Manfacturing.inventory_damaged_permission_new.CheckIns.ActiveControl = PL.Manfacturing.inventory_damaged_permission_new.CheckIns.dataGridView1;
            }
            else if (OwnerName == "inventory_import_permission_new")
            {
                inventory_import_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                inventory_import_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                inventory_import_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                inventory_import_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                inventory_import_permission_new.CheckIns.dataGridView1.CommitEdit(DataGridViewDataErrorContexts.Commit);
                inventory_import_permission_new.CheckIns.dataGridView1.EndEdit(DataGridViewDataErrorContexts.Commit);
            }
            else if (OwnerName == "Manfacture_Order_New_Form")
            {
                
                foreach (Form frm in Application.OpenForms)
                {
                    if (frm.Name == "Manfacture_Order_New_Form")
                    {
                        PL.Manfacturing.Manfacture_Order_New_Form manf_old = (PL.Manfacturing.Manfacture_Order_New_Form)frm;
                        if (int.Parse(manf_old.LblManfCount.Text) == InvCount)
                        {
                            if (Pro_Kind.Equals("منتج مصنع"))
                            {
                                manf_old.DGVFinishedProduct.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                                manf_old.DGVFinishedProduct.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                                manf_old.DGVFinishedProduct.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                                manf_old.DGVFinishedProduct.CommitEdit(DataGridViewDataErrorContexts.Commit);
                                manf_old.DGVFinishedProduct.EndEdit(DataGridViewDataErrorContexts.Commit);
                                manf_old.ActiveControl = manf_old.DGVFinishedProduct;
                            }
                            else
                            {
                                manf_old.DGVRawMaterial.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                                manf_old.DGVRawMaterial.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                                manf_old.DGVRawMaterial.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                                manf_old.DGVRawMaterial.CommitEdit(DataGridViewDataErrorContexts.Commit);
                                manf_old.DGVRawMaterial.EndEdit(DataGridViewDataErrorContexts.Commit);
                                manf_old.ActiveControl = manf_old.DGVRawMaterial;
                            }
                        }
                        
                    }
                }
            }
            else if (OwnerName == "Manfacture_Order_Assymetric_Component_New_Form")
            {
                foreach (Form frm in Application.OpenForms)
                {
                    if (frm.Name == "Manfacture_Order_Assymetric_Component_New_Form")
                    {
                        PL.Manfacturing.Manfacture_Order_Assymetric_Component_New_Form manf_old = (PL.Manfacturing.Manfacture_Order_Assymetric_Component_New_Form)frm;
                        if (int.Parse(manf_old.LblManfCount.Text) == InvCount)
                        {
                            if (Pro_Kind.Equals("منتج مصنع"))
                            {
                                manf_old.DGVFinishedProduct.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                                manf_old.DGVFinishedProduct.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                                manf_old.DGVFinishedProduct.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                                manf_old.DGVFinishedProduct.CommitEdit(DataGridViewDataErrorContexts.Commit);
                                manf_old.DGVFinishedProduct.EndEdit(DataGridViewDataErrorContexts.Commit);
                                manf_old.ActiveControl = manf_old.DGVFinishedProduct;
                            }
                            else
                            {
                                manf_old.DGVRawMaterial.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                                manf_old.DGVRawMaterial.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                                manf_old.DGVRawMaterial.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                                manf_old.DGVRawMaterial.CommitEdit(DataGridViewDataErrorContexts.Commit);
                                manf_old.DGVRawMaterial.EndEdit(DataGridViewDataErrorContexts.Commit);
                                manf_old.ActiveControl = manf_old.DGVRawMaterial;
                            }
                        }
                    }
                }
            }
            else if (OwnerName == "inventory_export_permission_new")
            {
                inventory_export_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                inventory_export_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                inventory_export_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                inventory_export_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                inventory_export_permission_new.CheckIns.dataGridView1.CommitEdit(DataGridViewDataErrorContexts.Commit);
                inventory_export_permission_new.CheckIns.dataGridView1.EndEdit(DataGridViewDataErrorContexts.Commit);
            }
            else if (OwnerName == "inventory_relocate_permission_new")
            {
                PL.Manfacturing.inventory_relocate_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                PL.Manfacturing.inventory_relocate_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_id"].Value = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                PL.Manfacturing.inventory_relocate_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_id2"].Value = dataGridView1.CurrentRow.Cells["pro_id2"].Value.ToString();
                PL.Manfacturing.inventory_relocate_permission_new.CheckIns.dataGridView1.CurrentRow.Cells["pro_name"].Value = dataGridView1.CurrentRow.Cells["pro_name"].Value.ToString();
                PL.Manfacturing.inventory_relocate_permission_new.CheckIns.dataGridView1.CommitEdit(DataGridViewDataErrorContexts.Commit);
                PL.Manfacturing.inventory_relocate_permission_new.CheckIns.dataGridView1.EndEdit(DataGridViewDataErrorContexts.Commit);
            }
            else if (OwnerName == "Maintenance_Spare_Parts_Form")
            {
                PL.invoices.Maintenance_Spare_Parts_Form.CheckIns.dataGridView1.CurrentRow.Cells["barcode"].Value = null;
                PL.invoices.Maintenance_Spare_Parts_Form.CheckIns.LblProId.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();
                PL.invoices.Maintenance_Spare_Parts_Form.CheckIns.TxtProName.Text = dataGridView1.CurrentRow.Cells[2].Value.ToString();
                PL.invoices.Maintenance_Spare_Parts_Form.CheckIns.ActiveControl = PL.invoices.Maintenance_Spare_Parts_Form.CheckIns.BtnSearch;
            }
            else if (OwnerName == "Barcode")
            {
                PL.Manfacturing.Barcode.CheckIns.LblProId.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();
                PL.Manfacturing.Barcode.CheckIns.TxtProName.Text = dataGridView1.CurrentRow.Cells[2].Value.ToString();
            }
            else if (OwnerName == "Kitchen_Procucts_New_Form")
            {
                PL.Restaurant.Kitchen_Procucts_New_Form.CheckIns.TxtProID.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();
                PL.Restaurant.Kitchen_Procucts_New_Form.CheckIns.TxtProName.Text = dataGridView1.CurrentRow.Cells[2].Value.ToString();
            }
            else if (OwnerName == "Pro_movement_Quantity")
            {
                PL.reports.stock.Pro_movement_Quantity.CheckIns.TxtProID.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();

                PL.reports.stock.Pro_movement_Quantity.CheckIns.TxtProName.Text = dataGridView1.CurrentRow.Cells[2].Value.ToString();
                PL.reports.stock.Pro_movement_Quantity.CheckIns.ActiveControl = PL.reports.stock.Pro_movement_Quantity.CheckIns.BtnSearch;
            }
            else if (OwnerName == "Pro_Movement_Quantity_Price_Form")
            {
                PL.reports.stock.Pro_Movement_Quantity_Price_Form.CheckIns.TxtProID.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();

                PL.reports.stock.Pro_Movement_Quantity_Price_Form.CheckIns.TxtProName.Text = dataGridView1.CurrentRow.Cells[2].Value.ToString();
                PL.reports.stock.Pro_Movement_Quantity_Price_Form.CheckIns.ActiveControl = PL.reports.stock.Pro_Movement_Quantity_Price_Form.CheckIns.BtnSearch;
            }
            else if (OwnerName == "Pro_Movement_Profit_Form")
            {
                PL.reports.stock.Pro_Movement_Profit_Form.CheckIns.TxtProID.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();

                PL.reports.stock.Pro_Movement_Profit_Form.CheckIns.TxtProName.Text = dataGridView1.CurrentRow.Cells[2].Value.ToString();
                PL.reports.stock.Pro_Movement_Profit_Form.CheckIns.ActiveControl = PL.reports.stock.Pro_Movement_Profit_Form.CheckIns.BtnSearch;
            }
            else if (OwnerName == "Pro_Movemet_Expirey_Form")
            {
                PL.reports.stock.Pro_Movemet_Expirey_Form.CheckIns.TxtProID.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();

                PL.reports.stock.Pro_Movemet_Expirey_Form.CheckIns.TxtProName.Text = dataGridView1.CurrentRow.Cells[2].Value.ToString();
                PL.reports.stock.Pro_Movemet_Expirey_Form.CheckIns.ActiveControl = PL.reports.stock.Pro_Movemet_Expirey_Form.CheckIns.BtnSearch;
            }
            else if (OwnerName == "Pro_Movement_Serial_Form")
            {
                PL.reports.stock.Pro_Movement_Serial_Form.CheckIns.TxtProID.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();

                PL.reports.stock.Pro_Movement_Serial_Form.CheckIns.TxtProName.Text = dataGridView1.CurrentRow.Cells[2].Value.ToString();
                PL.reports.stock.Pro_Movement_Serial_Form.CheckIns.ActiveControl = PL.reports.stock.Pro_Movement_Serial_Form.CheckIns.BtnSearch;
            }

        }
        //public bool CheckIfItemQuantityWillBeUnderZero() 
        //{
        //    bool UnderZero;
        //                bool IsService = (bool)dataGridView1.CurrentRow.Cells["is_service"].Value;
        //    decimal ItemCurrentBalance = GetItemQuantityInSmallUnit(dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString(),)
        //    decimal ItemFuturedBalacne = ItemCurrentBalance - Quantity;
        //        if (IsService == false && ( PermissionKind == 1 && ItemFuturedBalacne  <= 0))
        //        {
        //            UnderZero = true;
        //        }
        //        else 
        //        {
        //            UnderZero = true;
        //        }
        //    return UnderZero;
        //}
        private void dataGridView1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (dataGridView1.RowCount > 0)
            {
                if (e.KeyChar == (char)Keys.Return)
                {
                    AddProductToInvoice();
                    this.Close();
                }
                else
                {
                    TxtProName.Text += e.KeyChar;
                    TxtProName.Select(TxtProName.Text.Length, 0);
                    TxtProName.Focus();
                }
            }
        }

        private void dataGridView1_MouseClick(object sender, MouseEventArgs e)
        {
            if (dataGridView1.RowCount > 0)
            {
                AddProductToInvoice();
                this.Close();
            }
        }

        private void dataGridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
            }
        }

        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (dataGridView1.Rows.Count > 0)
            {
                if (e.KeyCode == Keys.Enter)
                {
                    AddProductToInvoice();
                    this.Close();
                }
                else if (e.KeyCode == Keys.Down)
                {
                    if ((dataGridView1.CurrentRow.Index + 1) < dataGridView1.Rows.Count)
                    {
                        dataGridView1.CurrentRow.Selected = false;
                        dataGridView1.Focus();
                        dataGridView1.Rows[dataGridView1.CurrentRow.Index + 1].Selected = true;
                        dataGridView1.CurrentCell = dataGridView1.Rows[dataGridView1.CurrentRow.Index + 1].Cells[dataGridView1.CurrentCell.ColumnIndex];
                    }
                    else
                    {
                        dataGridView1.CurrentRow.Selected = false;
                        dataGridView1.Focus();
                        dataGridView1.Rows[dataGridView1.CurrentRow.Index].Selected = true;
                    }
                }

            }
        }

        private void Search_Product_Form_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Escape)
            {
                this.Close();
            }
            else if (e.KeyChar == (char)Keys.Enter)
            {
                AddProductToInvoice();
                this.Close();
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void CMBCat_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!FormLoading)
            {
                SearchForProduct();
            }
        }

        private void TxtProId_TextChanged(object sender, EventArgs e)
        {
            SearchForProduct();
        }

        private void TxtManf_TextChanged(object sender, EventArgs e)
        {
            SearchForProduct();
        }

        private void BtnAddPro_Click(object sender, EventArgs e)
        {
            int CurrentProCount;
            List<int> ProCountList = new List<int>();
            foreach (Form frm in Application.OpenForms)
            {
                if (frm.Name == "item_new")
                {
                    item_new itm_old = (item_new)frm;
                    CurrentProCount = int.Parse(itm_old.LblProCount.Text);
                    ProCountList.Add(CurrentProCount);
                }
            }
            item_new itm_new = new item_new();
            if (ProCountList.Count > 0)
            {
                itm_new.LblProCount.Text = ((ProCountList.Max() + 1) + 1).ToString();
            }
            else
            {
                itm_new.LblProCount.Text = "1";
            }
            itm_new.label1.Text = "اضافة صنف : جديد";
            itm_new.Text = "اضافة صنف : جديد";
            itm_new.ShowDialog(this);
        }

        private void CbStock_SelectedIndexChanged(object sender, EventArgs e)
        {
            SearchForProduct();
        }

        public string GetProBalanceByProIDAndStockID(int RowIndex)
        {
            string ProBalance = string.Empty;
            if (dataGridView1.SelectedRows.Count > 0)
            {

                BL.StockBL stck_bl = new BL.StockBL();
                string ProID = dataGridView1.Rows[RowIndex].Cells["pro_id"].Value.ToString();
                string StockID = (CbStock.SelectedValue == null) ? null : CbStock.SelectedValue.ToString();
                DataTable dt = stck_bl.GetProDetailedBalanceByProIDAndStockID(ProID, StockID);
                if (dt.Rows.Count > 0)
                {
                    ProBalance = dt.Rows[0]["balance"].ToString();
                }
                else
                {
                    ProBalance = "0";
                }
            }
            return ProBalance;
        }

        //public string GetProSmallBalanceByProIDAndStockID()
        //{
        //    string ItemCurrentBalance = string.Empty;
        //    BL.Stock stck_bl = new BL.Stock();
        //    string ProID = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
        //    string StockID = (CbStock.SelectedValue == null) ? null : CbStock.SelectedValue.ToString();
        //    string CurrentBalance = stck_bl.GetProductSmallBalanceByProIDAndStockID(ProID, StockID);

        //        ItemCurrentBalance = decimal.Parse(CurrentBalance);

        //    return ItemCurrentBalance;
        //}

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {

        }

        private void TxtProId2_TextChanged(object sender, EventArgs e)
        {
            SearchForProduct();
        }

        public void NewItem()
        {
            int CurrentProCount;
            List<int> ProCountList = new List<int>();
            foreach (Form frm in Application.OpenForms)
            {
                if (frm.Name == "item_new")
                {
                    item_new itm_old = (item_new)frm;
                    CurrentProCount = int.Parse(itm_old.LblProCount.Text);
                    ProCountList.Add(CurrentProCount);
                }
            }
            item_new itm_new = new item_new();
            if (ProCountList.Count > 0)
            {
                itm_new.LblProCount.Text = (ProCountList.Max() + 1) .ToString();
            }
            else
            {
                itm_new.LblProCount.Text = "1";
            }
            itm_new.label1.Text = "اضافة صنف : جديد";
            itm_new.Text = "اضافة صنف : جديد";
            itm_new.ShowDialog(this);
        }

        public void UpdateItem()
        {
            if (dataGridView1.RowCount > 0)
            {
                int CurrentProCount;
                List<int> ProCountList = new List<int>();
                foreach (Form frm in Application.OpenForms)
                {
                    if (frm.Name == "item_new")
                    {
                        item_new itm_old = (item_new)frm;
                        CurrentProCount = int.Parse(itm_old.LblProCount.Text);
                        ProCountList.Add(CurrentProCount);
                    }
                }
                item_new itm_new = new item_new();
                if (ProCountList.Count > 0)
                {
                    itm_new.LblProCount.Text = (ProCountList.Max() + 1) .ToString();
                }
                else
                {
                    itm_new.LblProCount.Text = "1";
                }
                itm_new.label1.Text = "تعديل صنف";
                itm_new.Text = "تعديل الصنف : " + dataGridView1.CurrentRow.Cells[1].Value.ToString();
                itm_new.TxtId.Text = dataGridView1.CurrentRow.Cells[0].Value.ToString();
                itm_new.ShowDialog(this);
            }
        }

        public void DeleteItem()
        {
            if (dataGridView1.RowCount > 0)
            {
                if (MessageBox.Show("هل تريد فعلا حذف المنتج المحدد؟", "عملية الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) == DialogResult.Yes)
                {
                    string result;
                    BE.ProductBE mypro = new BE.ProductBE();
                    BL.ProductBL ProBL = new BL.ProductBL();
                    mypro.ProID = dataGridView1.CurrentRow.Cells[0].Value.ToString();
                    mypro.User_Name = Form1.CheckIns.UserName;
                    result = ProBL.DeleteProduct(mypro);
                    if (result == "success")
                    {
                        toolStripStatusLabel1.Text = "تم حذف المنتج بنجاح";
                        SearchForProduct();
                        timer1.Start();
                    }
                    else
                    {
                        MessageBox.Show("فشل الحذف : " + result);
                    }
                }
            }
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            switch (keyData)
            {
                case Keys.F1:
                    if (BtnNew.Visible)
                    {
                        NewItem();
                    }
                    break;
                case Keys.F2:
                    if (BtnEdit.Visible)
                    {
                        UpdateItem();
                    }
                    break;
                case Keys.F3:
                    if (BtnDelete.Visible)
                    {
                        DeleteItem();
                    }
                    break;
                case Keys.Escape:
                    this.Close();
                    break;
                default:
                    break;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }

        private void BtnNew_Click(object sender, EventArgs e)
        {
            NewItem();
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            UpdateItem();
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DeleteItem();
        }

        private void BtnItemMovement_Click(object sender, EventArgs e)
        {
            if (dataGridView1.RowCount > 0)
            {
                if (PL.reports.stock.Pro_movement_Quantity.CheckIns == null)
                {
                    PL.reports.stock.Pro_movement_Quantity.CreateIns.TxtProID.Text = dataGridView1.CurrentRow.Cells["pro_id"].Value.ToString();
                    PL.reports.stock.Pro_movement_Quantity.CreateIns.GetProductMovement();
                    PL.reports.stock.Pro_movement_Quantity.CreateIns.ShowDialog();
                }

            }

        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnUpdate_Click(object sender, EventArgs e)
        {
            SearchForProduct();
        }
    }
}
